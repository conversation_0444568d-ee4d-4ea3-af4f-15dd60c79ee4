------------------------------------------------------------------------
Intel(R) Threading Building Blocks - Release Notes
                   Version 2020
------------------------------------------------------------------------

Intel(R) Threading Building Blocks (Intel(R) TBB) is planned to be
improved through renewal and update with latest C++ standards to increase
usability. Evaluation of some Intel(R) TBB features removal will be
conducted in some future releases. Features under consideration are mapped
to updated options as described in the 'TBB Revamp: Background,Changes,
and Modernization'
(see https://software.intel.com/en-us/articles/tbb-revamp).

System Requirements
-------------------

Intel(R) Threading Building Blocks is available commercially
(see http://software.intel.com/en-us/intel-tbb) as a binary distribution,
and in open source, in both source and binary forms
(see https://github.com/intel/tbb).

When built from source, Intel(R) TBB is intended to be highly portable
and so supports a wide variety of operating systems and platforms (see
http://threadingbuildingblocks.org for more details).

Binary distributions, including commercial distributions, are validated
and officially supported for the hardware, software, operating systems
and compilers listed here.

Hardware - Recommended

    Microsoft* Windows* Systems
        Intel(R) Core(TM) processor family
        Intel(R) Xeon(R) processor family
    Linux* Systems
        Intel(R) Core(TM) processor family
        Intel(R) Xeon(R) processor family
    macOS* Systems
        Intel(R) Core(TM) processor family
    Android* Systems
        Intel(R) Atom(TM) processor family

Hardware - Supported

    Intel(R) Pentium(R) processor family
    Intel(R) Xeon Phi(TM) processor family
    Intel(R) Atom(TM) processor family
    Non Intel(R) processors compatible with the above processors

Software - Minimum Requirements

    Supported operating system (see below)
    Supported compiler (see below)

Software - Recommended

    Intel(R) Parallel Studio XE 2019, 2020
    Intel(R) System Studio 2019, 2020

Software - Supported Operating Systems

    Systems with Microsoft* Windows* operating systems
        Microsoft* Windows* 10
        Microsoft* Windows* Server 2016
        Microsoft* Windows* Server 2019
    Systems with Linux* operating systems
        CentOS* 7.1
        Debian* 9, 10
        Fedora* 30
        Intel(R) Cluster Ready
        Red Hat* Enterprise Linux* 7, 8
        SuSE* Linux* Enterprise Server 12,15
        Ubuntu* 16.04 LTS, 18.04 LTS
        Ubuntu* 19.04
        WindRiver* Linux 9
        Yocto* 2.3
    Systems with OS X* or macOS* operating systems
        macOS* 10.14, 10.15
    Systems with Android* operating systems
        Android* 6.x, 7.x, 8.x, 9.x

Software - Supported Compilers

    Intel(R) C++ Compiler 18, 19 and 19.1 version
    Microsoft* Visual C++ 14.1 (Microsoft* Visual Studio* 2017,
        Windows* OS only)
    Microsoft* Visual C++ 14.2 (Microsoft* Visual Studio* 2019,
        Windows* OS only)
    Microsoft* Windows* Software Development Kit for Windows* 8.1
    Microsoft* Windows* Software Development Kit for Windows* 10
    For each supported Linux* operating system, the standard gcc
        version provided with that operating system is supported
            GNU Compilers (gcc) 4.8 - 9.1
            GNU C Library (glibc) version 2.17 - 2.29
    Clang* 3.8 - 8.0
    Xcode* 8.x - 11.x
    Android* NDK r10e - r17b

Software - Supported Performance Analysis Tools

    Intel(R) VTune(TM) Amplifier XE 2019, 2020
    Intel(R) Inspector XE 2019, 2020
    Intel(R) Advisor XE 2019, 2020

Known Issues
------------

Below is the list of known issues in this release of
Intel(R) Threading Building Blocks (Intel(R) TBB).
See the "Known Issues" appendix in the Intel(R) TBB Developer
Reference for notes applicable to multiple releases of Intel(R) TBB.

Library Issues

    - If you build Intel(R) TBB from sources with GCC 6 or higher,
        specify the -flifetime-dse=1 option to prevent crashes at
        runtime, or use Intel(R) TBB makefiles that automatically set
        this option.

------------------------------------------------------------------------
(C) 2020 Intel Corporation

Intel, the Intel logo, Intel Core, Intel Atom, Celeron, Xeon,
Intel Xeon Phi and Pentium are trademarks of Intel Corporation
in the U.S. and/or other countries

* Other names and brands may be claimed as the property of others.

Third Party and Open Source Licenses

Content of some examples or binaries may be covered by various open-source
licenses. See the index.html file in each respective folder for details.
