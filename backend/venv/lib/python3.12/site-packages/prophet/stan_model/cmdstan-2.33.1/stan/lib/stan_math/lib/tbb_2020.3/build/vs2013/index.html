<HTML>
<BODY>

<H2>Overview</H2>
This directory contains the Visual Studio* 2013 solution to build Intel&reg; Threading Building Blocks.


<H2>Files</H2>
<DL>
<DT><A HREF="makefile.sln">makefile.sln</A>
<DD>Solution file.</DD>
<DT><A HREF="tbb.vcxproj">tbb.vcxproj</A>
<DD>Library project file.</DD>
<DT><A HREF="tbbmalloc.vcxproj">tbbmalloc.vcxproj</A>
<DD>Scalable allocator library project file.</DD>
<DT><A HREF="tbbmalloc_proxy.vcxproj">tbbmalloc_proxy.vcxproj</A>
<DD>Standard allocator replacement project file. </DD>
</DL>

<HR>
<A HREF="../index.html">Up to parent directory</A>
<P></P>
Copyright &copy; 2017-2020 Intel Corporation.  All Rights Reserved.
<P></P>
Intel and the Intel logo are trademarks of Intel Corporation
or its subsidiaries in the U.S. and/or other countries.
<p></p>
* Other names and brands may be claimed as the property of others.
</BODY>
</HTML>
