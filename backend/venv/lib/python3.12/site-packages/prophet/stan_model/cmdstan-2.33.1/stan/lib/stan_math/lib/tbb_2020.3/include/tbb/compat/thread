/*
    Copyright (c) 2005-2020 Intel Corporation

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
*/

#include "../internal/_deprecated_header_message_guard.h"

#if !defined(__TBB_show_deprecation_message_thread_H) && defined(__TBB_show_deprecated_header_message)
#define  __TBB_show_deprecation_message_thread_H
#pragma message("TBB Warning: tbb/compat/thread is deprecated. For details, please see Deprecated Features appendix in the TBB reference manual.")
#endif

#if defined(__TBB_show_deprecated_header_message)
#undef __TBB_show_deprecated_header_message
#endif

#ifndef __TBB_thread_H
#define __TBB_thread_H

#define __TBB_thread_H_include_area
#include "../internal/_warning_suppress_enable_notice.h"

#include "../tbb_config.h"

#if TBB_IMPLEMENT_CPP0X

#include "../tbb_thread.h"

namespace std {

typedef tbb::tbb_thread thread;

namespace this_thread {
    using tbb::this_tbb_thread::get_id;
    using tbb::this_tbb_thread::yield;

    __TBB_DEPRECATED_IN_VERBOSE_MODE inline void sleep_for(const tbb::tick_count::interval_t& rel_time) {
        tbb::internal::thread_sleep_v3( rel_time );
    }
}

} // namespace std

#else /* TBB_IMPLEMENT_CPP0X */

#define __TBB_COMPAT_THREAD_RECURSION_PROTECTOR 1
#include <thread>
#undef __TBB_COMPAT_THREAD_RECURSION_PROTECTOR

#endif /* TBB_IMPLEMENT_CPP0X */

#include "../internal/_warning_suppress_disable_notice.h"
#undef __TBB_thread_H_include_area

#else /* __TBB_thread_H */

#if __TBB_COMPAT_THREAD_RECURSION_PROTECTOR
#error The tbb/compat/thread header attempts to include itself. \
       Please make sure that {TBBROOT}/include/tbb/compat is NOT in include paths.
#endif

#endif /* __TBB_thread_H */
