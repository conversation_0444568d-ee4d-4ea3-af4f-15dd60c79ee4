# 1 "../tbb_2020.3/src/tbbmalloc/lin64-tbbmalloc-export.def"
# 1 "/project/python/build/lib.linux-x86_64-cpython-39/prophet/stan_model/cmdstan-2.33.1/stan/lib/stan_math/lib/tbb//"
# 1 "<built-in>"
# 1 "<command-line>"
# 1 "/usr/include/stdc-predef.h" 1 3 4
# 1 "<command-line>" 2
# 1 "../tbb_2020.3/src/tbbmalloc/lin64-tbbmalloc-export.def"
# 17 "../tbb_2020.3/src/tbbmalloc/lin64-tbbmalloc-export.def"
{
global:

scalable_calloc;
scalable_free;
scalable_malloc;
scalable_realloc;
scalable_posix_memalign;
scalable_aligned_malloc;
scalable_aligned_realloc;
scalable_aligned_free;
scalable_msize;
scalable_allocation_mode;
scalable_allocation_command;
__TBB_malloc_safer_aligned_msize;
__TBB_malloc_safer_aligned_realloc;
__TBB_malloc_safer_free;
__TBB_malloc_safer_msize;
__TBB_malloc_safer_realloc;


_ZN3rml11pool_createElPKNS_13MemPoolPolicyE;
_ZN3rml14pool_create_v1ElPKNS_13MemPoolPolicyEPPNS_10MemoryPoolE;
_ZN3rml10pool_resetEPNS_10MemoryPoolE;
_ZN3rml11pool_mallocEPNS_10MemoryPoolEm;
_ZN3rml12pool_destroyEPNS_10MemoryPoolE;
_ZN3rml9pool_freeEPNS_10MemoryPoolEPv;
_ZN3rml12pool_reallocEPNS_10MemoryPoolEPvm;
_ZN3rml20pool_aligned_reallocEPNS_10MemoryPoolEPvmm;
_ZN3rml19pool_aligned_mallocEPNS_10MemoryPoolEmm;
_ZN3rml13pool_identifyEPv;
_ZN3rml10pool_msizeEPNS_10MemoryPoolEPv;

local:


*3rml*;
*3tbb*;
*__TBB*;
__itt_*;
ITT_DoOneTimeInitialization;
TBB_runtime_interface_version;


__intel_*;
_intel_*;
get_memcpy_largest_cachelinesize;
get_memcpy_largest_cache_size;
get_mem_ops_method;
init_mem_ops_method;
irc__get_msg;
irc__print;
override_mem_ops_method;
set_memcpy_largest_cachelinesize;
set_memcpy_largest_cache_size;

};
