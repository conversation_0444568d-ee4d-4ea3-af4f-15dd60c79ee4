/*
    Copyright (c) 2005-2020 Intel Corporation

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
*/

#include "tbb/tbb_config.h"

#if (!defined(TBB_SUPPRESS_DEPRECATED_MESSAGES) || (TBB_SUPPRESS_DEPRECATED_MESSAGES == 0)) && !defined(__TBB_INTERNAL_INCLUDES_DEPRECATION_MESSAGE) && \
!defined(__TBB_condition_variable_H_include_area) && \
!defined(__TBB_ppl_H_include_area) && \
!defined(__TBB_thread_H_include_area) && \
!defined(__TBB_tuple_H_include_area) && \
!defined(__TBB_aggregator_H_include_area) && \
!defined(__TBB_aligned_space_H_include_area) && \
!defined(__TBB_atomic_H_include_area) && \
!defined(__TBB_combinable_H_include_area) && \
!defined(__TBB_concurrent_hash_map_H_include_area) && \
!defined(__TBB_concurrent_lru_cache_H_include_area) && \
!defined(__TBB_concurrent_map_H_include_area) && \
!defined(__TBB_concurrent_priority_queue_H_include_area) && \
!defined(__TBB_concurrent_queue_H_include_area) && \
!defined(__TBB_concurrent_set_H_include_area) && \
!defined(__TBB_concurrent_unordered_map_H_include_area) && \
!defined(__TBB_concurrent_unordered_set_H_include_area) && \
!defined(__TBB_concurrent_vector_H_include_area) && \
!defined(__TBB_critical_section_H_include_area) && \
!defined(__TBB_enumerable_thread_specific_H_include_area) && \
!defined(__TBB_flow_graph_opencl_node_H_include_area) && \
!defined(__TBB_flow_graph_H_include_area) && \
!defined(__TBB_mutex_H_include_area) && \
!defined(__TBB_parallel_do_H_include_area) && \
!defined(__TBB_parallel_for_H_include_area) && \
!defined(__TBB_parallel_invoke_H_include_area) && \
!defined(__TBB_parallel_reduce_H_include_area) && \
!defined(__TBB_parallel_scan_H_include_area) && \
!defined(__TBB_parallel_sort_H_include_area) && \
!defined(__TBB_parallel_while_H_include_area) && \
!defined(__TBB_partitioner_H_include_area) && \
!defined(__TBB_pipeline_H_include_area) && \
!defined(__TBB_queuing_mutex_H_include_area) && \
!defined(__TBB_queuing_rw_mutex_H_include_area) && \
!defined(__TBB_reader_writer_lock_H_include_area) && \
!defined(__TBB_recursive_mutex_H_include_area) && \
!defined(__TBB_runtime_loader_H_include_area) && \
!defined(__TBB_task_scheduler_init_H_include_area) && \
!defined(__TBB_spin_mutex_H_include_area) && \
!defined(__TBB_task_arena_H_include_area) && \
!defined(__TBB_task_group_H_include_area) && \
!defined(__TBB_task_scheduler_observer_H_include_area) && \
!defined(__TBB_task_H_include_area) && \
!defined(__TBB_tbb_exception_H_include_area) && \
!defined(__TBB_tbb_profiling_H_include_area) && \
!defined(__TBB_tbb_thread_H_include_area) && \
!defined(__TBB_tbb_H_include_area)

#define __TBB_show_deprecated_header_message

#endif
