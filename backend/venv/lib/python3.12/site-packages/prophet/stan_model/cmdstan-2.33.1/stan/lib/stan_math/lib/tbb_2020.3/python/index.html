<HTML>
<BODY>
<H2>Python* API for Intel&reg; Threading Building Blocks (Intel&reg; TBB).
</H2>

<H2>Overview</H2>
It is a preview Python* module which unlocks opportunities for additional performance in multi-threaded and multiprocess Python programs by enabling threading composability
between two or more thread-enabled libraries like Numpy, Scipy, Sklearn, Dask, Joblib, and etc.
<p></p>
The biggest improvement can be achieved when a task pool like the ThreadPool or Pool from the Python standard library or libraries like Dask or Joblib (used either in multi-threading or multi-processing mode)
execute tasks calling compute-intensive functions of Numpy/Scipy/Sklearn/PyDAAL which in turn are parallelized using Intel&reg; Math Kernel Library or/and Intel&reg; TBB.
<p></p>
The module implements Pool class with the standard interface using Intel&reg; TBB which can be used to replace Python's ThreadPool.
Thanks to the monkey-patching technique implemented in class Monkey, no source code change is needed in order to enable threading composability in Python programs.
<p></p>
For more information and examples, please refer to <A HREF="http://software.intel.com/en-us/blogs/2016/04/04/unleash-parallel-performance-of-python-programs">online blog</A>.

<H2>Directories</H2>
<DL>
<DT><A HREF="rml">rml</A>
<DD>The folder contains sources for building the plugin with cross-process dynamic thread scheduler implementation.
<DT><A HREF="tbb">tbb</A>
<DD>The folder contains Python module sources.
</DL>

<H2>Files</H2>
<DL>
<DT><A HREF="setup.py">setup.py</A>
<DD>Standard Python setup script.
<DT><A HREF="Makefile">Makefile</A>
<DD>Internal Makefile for building, installing, and testing. See below.
<DT><A HREF="TBB.py">TBB.py</A>
<DD>Alternative entry point for Python module.
</DL>

<A NAME=build><H2>Build and install (source package only)</H2></A>
For accessing targets defined in python/Makefile, please use
<A HREF="../src/index.html">src/Makefile</A>
instead and build runtime libraries before working with Python.
<DL>
<DT><TT>make -C ../src python_all</TT>
<DD>Install and test as described below.
<DT><TT>make -C ../src python_install</TT>
<DD>Install module into Python environment.
<DT><TT>make -C ../src python_test</TT>
<DD>Test installed Intel&reg; TBB module for Python.
<DT><TT>make -C ../src python_release</TT>
<DD>Recompile Python module. Result is located in Intel&reg; TBB build directory.
<DT><TT>make  -C ../src python_clean</TT>
<DD>Remove any intermediate files produced by the commands above. Does not remove installed module.
</DL>

<H2>Command-line interface</H2>
<DL>
<DT><TT>python -m tbb -h</TT>
<DD>Print documentation on command-line interface</DD>
<DT><TT>pydoc tbb</TT>
<DD>Read built-in documentation for Python interfaces.</DD>
<DT><TT>python-tbb your_script.py</TT>
<DT><TT>python -m tbb your_script.py</TT>
<DD>Run your_script.py in context of `with tbb.Monkey():` when Intel&reg; TBB is enabled. By default only multi-threading will be covered.</DD>
<DT><TT>python -m tbb --ipc your_script.py</TT>
<DD>Run your_script.py in context of `with tbb.Monkey():` when Intel&reg; TBB enabled in both multi-threading and multi-processing modes.</DD>
</DL>

<H2>System Requirements</H2>
The Python module was not tested on older versions of Python thus we require at least Python versions 2.7 and 3.5 or higher.<BR>
SWIG must be of version 3.0.6 or higher<BR>
OS versions:
Microsoft* Windows* Server 2012,
Microsoft* Windows* 10,
Ubuntu* 14.04 LTS,
Red Hat* Enterprise Linux* 7.
<HR>
<A href="../index.html">Up to parent directory</A>
<p></p>
Copyright &copy; 2016-2020 Intel Corporation.  All Rights Reserved.
<P></P>
Intel is a registered trademark or trademark of Intel Corporation
or its subsidiaries in the United States and other countries.
<p></p>
* Other names and brands may be claimed as the property of others.
</BODY>
</HTML>
