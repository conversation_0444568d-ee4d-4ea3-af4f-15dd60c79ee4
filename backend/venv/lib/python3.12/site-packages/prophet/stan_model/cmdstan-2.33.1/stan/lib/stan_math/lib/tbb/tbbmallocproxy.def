# 1 "../tbb_2020.3/src/tbbmalloc/lin64-proxy-export.def"
# 1 "/project/python/build/lib.linux-x86_64-cpython-39/prophet/stan_model/cmdstan-2.33.1/stan/lib/stan_math/lib/tbb//"
# 1 "<built-in>"
# 1 "<command-line>"
# 1 "/usr/include/stdc-predef.h" 1 3 4
# 1 "<command-line>" 2
# 1 "../tbb_2020.3/src/tbbmalloc/lin64-proxy-export.def"
# 17 "../tbb_2020.3/src/tbbmalloc/lin64-proxy-export.def"
{
global:
calloc;
free;
malloc;
realloc;
posix_memalign;
memalign;
aligned_alloc;
valloc;
pvalloc;
mallinfo;
mallopt;
malloc_usable_size;
__libc_malloc;
__libc_realloc;
__libc_calloc;
__libc_free;
__libc_memalign;
__libc_pvalloc;
__libc_valloc;
__TBB_malloc_proxy;
_ZdaPv;
_ZdaPvRKSt9nothrow_t;
_ZdlPv;
_ZdlPvRKSt9nothrow_t;
_Znam;
_ZnamRKSt9nothrow_t;
_Znwm;
_ZnwmRKSt9nothrow_t;

local:


*3rml8internal*;
*3tbb*;
*__TBB*;

};
