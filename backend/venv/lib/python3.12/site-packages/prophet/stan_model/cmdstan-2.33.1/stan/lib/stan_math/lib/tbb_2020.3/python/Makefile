# Copyright (c) 2016-2020 Intel Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

tbb_root?=..
BUILDING_PHASE:=0
include $(tbb_root)/build/common.inc
.PHONY: all release test install test-install

export TBBROOT=$(abspath $(tbb_root))
SRC=$(tbb_root)/python/*.py $(tbb_root)/python/tbb/*
PY_SETUP=python $(tbb_root)/python/setup.py

all: install test

clean:
	$(PY_SETUP) clean -b$(work_dir)_release

release: CC=$(compiler)
release: $(SRC) rml
	$(PY_SETUP) build -b$(work_dir)_release -f check

install: CC=$(compiler)
install: $(SRC) rml
	$(PY_SETUP) build -b$(work_dir)_release build_ext -f -I$(tbb_root)/include -L$(work_dir)_release install -f

test:
	python -m tbb test

rml:
ifeq (linux,$(tbb_os))
	$(MAKE) -C "$(work_dir)_release" -rf $(tbb_root)/python/rml/Makefile cfg=release rml
rml_%:
	$(MAKE) -C "$(work_dir)_release" -rf $(tbb_root)/python/rml/Makefile cfg=release $(subst rml_,,$@)
endif
