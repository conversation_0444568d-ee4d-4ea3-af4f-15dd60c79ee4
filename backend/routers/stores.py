from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
import json
from datetime import datetime

from database import get_db
from models import User, Store, Product, Order
from routers.auth import get_current_user
from services.shopify_service import ShopifyGraphQLService
from services.forecasting_service import SalesForecastingService

router = APIRouter()


class StoreCreate(BaseModel):
    name: str  # Store name (e.g., "my-store" for my-store.myshopify.com)
    admin_access_token: str  # Admin API access token
    storefront_access_token: Optional[str] = None  # Storefront API access token (optional)


class StoreResponse(BaseModel):
    id: int
    name: str
    platform: str
    shop_domain: Optional[str] = None
    shop_name: Optional[str] = None
    is_active: bool
    last_sync: Optional[str] = None


class StoreConnectionTest(BaseModel):
    success: bool
    message: str
    store_info: Optional[dict] = None


@router.post("/", response_model=StoreResponse)
async def create_store(
    store: StoreCreate, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)
):
    # Test connection before saving
    connection_test = await test_store_connection(store)
    if not connection_test.success:
        raise HTTPException(status_code=400, detail=f"Failed to connect to store: {connection_test.message}")

    # Extract shop info from connection test
    shop_info = connection_test.store_info or {}
    shop_domain = f"{store.name}.myshopify.com"

    # Check if store already exists for this user
    existing_store = db.query(Store).filter(Store.name == store.name, Store.owner_id == current_user.id).first()

    if existing_store:
        raise HTTPException(status_code=400, detail="Store with this name already exists")

    db_store = Store(
        name=store.name,
        platform="shopify",
        admin_access_token=store.admin_access_token,
        storefront_access_token=store.storefront_access_token,
        shop_domain=shop_domain,
        shop_id=shop_info.get("id"),
        shop_name=shop_info.get("name"),
        owner_id=current_user.id,
    )
    db.add(db_store)
    db.commit()
    db.refresh(db_store)

    return db_store


@router.get("/", response_model=List[StoreResponse])
async def get_stores(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    stores = db.query(Store).filter(Store.owner_id == current_user.id).all()

    # Convert datetime to string for each store
    for store in stores:
        if store.last_sync:
            store.last_sync = store.last_sync.isoformat()

    return stores


@router.post("/test-connection", response_model=StoreConnectionTest)
async def test_store_connection(store: StoreCreate):
    """Test connection to Shopify store using GraphQL Admin API"""
    try:
        shop_domain = f"{store.name}.myshopify.com"
        shopify_service = ShopifyGraphQLService(
            shop_domain=shop_domain,
            admin_access_token=store.admin_access_token,
            storefront_access_token=store.storefront_access_token,
        )

        # Test Admin API connection
        admin_result = await shopify_service.test_admin_connection()
        if not admin_result["success"]:
            return StoreConnectionTest(success=False, message=admin_result["message"])

        # Test Storefront API connection if token provided
        storefront_result = None
        if store.storefront_access_token:
            storefront_result = await shopify_service.test_storefront_connection()

        return StoreConnectionTest(
            success=True,
            message="Successfully connected to Shopify store",
            store_info={
                "admin_api": admin_result.get("shop_info", {}),
                "storefront_api": storefront_result.get("shop_info") if storefront_result else None,
            },
        )

    except Exception as e:
        return StoreConnectionTest(success=False, message=f"Connection failed: {str(e)}")


@router.post("/{store_id}/sync")
async def sync_store_data(store_id: int, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """Sync store data using new Shopify GraphQL APIs"""
    store = db.query(Store).filter(Store.id == store_id, Store.owner_id == current_user.id).first()

    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    try:
        # Create Shopify service instance
        shopify_service = ShopifyGraphQLService(
            shop_domain=store.shop_domain,
            admin_access_token=store.admin_access_token,
            storefront_access_token=store.storefront_access_token,
        )

        # Sync products
        await sync_shopify_products(shopify_service, store, db)

        # Sync orders
        await sync_shopify_orders(shopify_service, store, db)

        # Update sales data for forecasting
        forecasting_service = SalesForecastingService(db)
        orders_data = await shopify_service.get_orders(limit=100)
        if orders_data.get("edges"):
            orders_list = [edge["node"] for edge in orders_data["edges"]]
            forecasting_service.update_sales_data_from_orders(orders_list)

        # Update last_sync timestamp
        store.last_sync = datetime.now()
        db.commit()

        return {"message": "Sync completed successfully", "store_id": store_id}
    except Exception as e:
        db.rollback()
        import traceback

        print(f"Sync error: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Sync failed: {str(e)}")


@router.delete("/{store_id}")
async def delete_store(store_id: int, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """Delete a store and all its associated data"""
    store = db.query(Store).filter(Store.id == store_id, Store.owner_id == current_user.id).first()

    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    try:
        # Delete associated products, orders, sales data, and forecasts
        from models import Product, Order, SalesData, ForecastData

        # Get all products for this store
        products = db.query(Product).filter(Product.store_id == store_id).all()
        product_ids = [p.id for p in products]

        # Delete forecast data
        if product_ids:
            db.query(ForecastData).filter(ForecastData.product_id.in_(product_ids)).delete(synchronize_session=False)

        # Delete sales data
        if product_ids:
            db.query(SalesData).filter(SalesData.product_id.in_(product_ids)).delete(synchronize_session=False)

        # Delete products
        db.query(Product).filter(Product.store_id == store_id).delete(synchronize_session=False)

        # Delete orders
        db.query(Order).filter(Order.store_id == store_id).delete(synchronize_session=False)

        # Delete the store
        db.delete(store)
        db.commit()

        return {"message": "Store and all associated data deleted successfully"}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to delete store: {str(e)}")


@router.get("/{store_id}/products/{product_id}/forecast")
async def get_product_forecast(
    store_id: int,
    product_id: int,
    days_ahead: int = 30,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get sales forecast for a specific product"""
    # Verify store ownership
    store = db.query(Store).filter(Store.id == store_id, Store.owner_id == current_user.id).first()
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Verify product belongs to store
    from models import Product

    product = db.query(Product).filter(Product.id == product_id, Product.store_id == store_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    # Generate forecast
    forecasting_service = SalesForecastingService(db)
    forecast_result = forecasting_service.get_product_forecast(product_id, days_ahead)

    return forecast_result


async def sync_shopify_products(shopify_service: ShopifyGraphQLService, store: Store, db: Session):
    """Sync products using new Shopify GraphQL service"""
    from models import Product

    try:
        cursor = None
        has_next_page = True

        while has_next_page:
            # Get products from Shopify
            products_data = await shopify_service.get_products(limit=50, cursor=cursor)

            for edge in products_data.get("edges", []):
                product_data = edge["node"]
                external_id = product_data["id"].split("/")[-1]

                # Get first variant for pricing
                variants = product_data.get("variants", {}).get("edges", [])
                variant = variants[0]["node"] if variants else {}

                # Get first image
                images = product_data.get("images", {}).get("edges", [])
                image_urls = [img["node"]["url"] for img in images] if images else []

                # Check if product exists
                existing_product = (
                    db.query(Product).filter(Product.external_id == external_id, Product.store_id == store.id).first()
                )

                product_info = {
                    "external_id": external_id,
                    "title": product_data.get("title", ""),
                    "description": product_data.get("description", ""),
                    "description_html": product_data.get("descriptionHtml", ""),
                    "handle": product_data.get("handle", ""),
                    "price": float(variant.get("price", 0)) if variant.get("price") else 0,
                    "compare_at_price": (
                        float(variant.get("compareAtPrice", 0)) if variant.get("compareAtPrice") else None
                    ),
                    "inventory_quantity": variant.get("inventoryQuantity", 0) or 0,
                    "sku": variant.get("sku", ""),
                    "barcode": variant.get("barcode", ""),
                    "weight": variant.get("weight", 0) or 0,
                    "weight_unit": variant.get("weightUnit", "kg"),
                    "status": product_data.get("status", "").lower(),
                    "product_type": product_data.get("productType", ""),
                    "vendor": product_data.get("vendor", ""),
                    "tags": json.dumps(product_data.get("tags", [])),
                    "images": json.dumps(image_urls),
                    "variants": json.dumps([v["node"] for v in variants]),
                    "store_id": store.id,
                }

                if existing_product:
                    # Update existing product
                    for key, value in product_info.items():
                        if key != "external_id":  # Don't update the ID
                            setattr(existing_product, key, value)
                else:
                    # Create new product
                    new_product = Product(**product_info)
                    db.add(new_product)

            # Check for next page
            page_info = products_data.get("pageInfo", {})
            has_next_page = page_info.get("hasNextPage", False)
            cursor = page_info.get("endCursor") if has_next_page else None

            # Commit after each page
            db.commit()

    except Exception as e:
        db.rollback()
        raise Exception(f"Error syncing products: {str(e)}")


async def sync_shopify_orders(shopify_service: ShopifyGraphQLService, store: Store, db: Session):
    """Sync orders using new Shopify GraphQL service"""
    from models import Order

    try:
        cursor = None
        has_next_page = True

        while has_next_page:
            # Get orders from Shopify
            orders_data = await shopify_service.get_orders(limit=50, cursor=cursor)

            for edge in orders_data.get("edges", []):
                order_data = edge["node"]
                external_id = order_data["id"].split("/")[-1]

                # Extract pricing information
                total_price_set = order_data.get("totalPriceSet", {}).get("shopMoney", {})
                subtotal_price_set = order_data.get("subtotalPriceSet", {}).get("shopMoney", {})
                total_tax_set = order_data.get("totalTaxSet", {}).get("shopMoney", {})
                total_discounts_set = order_data.get("totalDiscountsSet", {}).get("shopMoney", {})

                # Extract customer information
                customer = order_data.get("customer", {})

                # Extract line items
                line_items = order_data.get("lineItems", {}).get("edges", [])
                line_items_data = [item["node"] for item in line_items]

                # Check if order exists
                existing_order = (
                    db.query(Order).filter(Order.external_id == external_id, Order.store_id == store.id).first()
                )

                order_info = {
                    "external_id": external_id,
                    "order_number": order_data.get("name", ""),
                    "customer_email": order_data.get("email", ""),
                    "customer_id": customer.get("id", "").split("/")[-1] if customer.get("id") else None,
                    "total_price": float(total_price_set.get("amount", 0)) if total_price_set else 0,
                    "subtotal_price": float(subtotal_price_set.get("amount", 0)) if subtotal_price_set else 0,
                    "total_tax": float(total_tax_set.get("amount", 0)) if total_tax_set else 0,
                    "total_discounts": float(total_discounts_set.get("amount", 0)) if total_discounts_set else 0,
                    "currency": total_price_set.get("currencyCode", "USD") if total_price_set else "USD",
                    "status": order_data.get("displayFulfillmentStatus", "unfulfilled"),
                    "financial_status": order_data.get("displayFinancialStatus", "pending"),
                    "fulfillment_status": order_data.get("displayFulfillmentStatus", "unfulfilled"),
                    "order_date": datetime.fromisoformat(order_data.get("createdAt", "").replace("Z", "+00:00")),
                    "line_items": json.dumps(line_items_data),
                    "store_id": store.id,
                }

                if existing_order:
                    # Update existing order
                    for key, value in order_info.items():
                        if key != "external_id":  # Don't update the ID
                            setattr(existing_order, key, value)
                else:
                    # Create new order
                    new_order = Order(**order_info)
                    db.add(new_order)

            # Check for next page
            page_info = orders_data.get("pageInfo", {})
            has_next_page = page_info.get("hasNextPage", False)
            cursor = page_info.get("endCursor") if has_next_page else None

            # Commit after each page
            db.commit()

    except Exception as e:
        db.rollback()
        raise Exception(f"Error syncing orders: {str(e)}")
