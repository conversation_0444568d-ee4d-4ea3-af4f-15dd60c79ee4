from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from pydantic import BaseModel
import httpx
import asyncio
from datetime import datetime

# Optional imports for e-commerce platforms
try:
    import shopify

    SHOPIFY_AVAILABLE = True
except ImportError:
    SHOPIFY_AVAILABLE = False

try:
    from woocommerce import API as WooCommerceAPI

    WOOCOMMERCE_AVAILABLE = True
except ImportError:
    WOOCOMMERCE_AVAILABLE = False

from database import get_db
from models import User, Store
from routers.auth import get_current_user

router = APIRouter()


class StoreCreate(BaseModel):
    name: str
    platform: str  # 'shopify' or 'woocommerce'
    store_url: str
    api_key: str
    api_secret: str


class StoreResponse(BaseModel):
    id: int
    name: str
    platform: str
    store_url: str
    is_active: bool
    last_sync: str | None = None


class StoreConnectionTest(BaseModel):
    success: bool
    message: str
    store_info: dict = None


@router.post("/", response_model=StoreResponse)
async def create_store(
    store: StoreCreate, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)
):
    # Test connection before saving
    connection_test = await test_store_connection(store)
    if not connection_test.success:
        raise HTTPException(status_code=400, detail=f"Failed to connect to store: {connection_test.message}")

    db_store = Store(
        name=store.name,
        platform=store.platform,
        store_url=store.store_url,
        api_key=store.api_key,
        api_secret=store.api_secret,
        owner_id=current_user.id,
    )
    db.add(db_store)
    db.commit()
    db.refresh(db_store)

    return db_store


@router.get("/", response_model=List[StoreResponse])
async def get_stores(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    stores = db.query(Store).filter(Store.owner_id == current_user.id).all()
    
    # Convert datetime to string for each store
    for store in stores:
        if store.last_sync:
            store.last_sync = store.last_sync.isoformat()
            
    return stores


@router.post("/test-connection", response_model=StoreConnectionTest)
async def test_store_connection(store: StoreCreate):
    try:
        if store.platform == "shopify":
            return await test_shopify_connection(store)
        elif store.platform == "woocommerce":
            return await test_woocommerce_connection(store)
        else:
            return StoreConnectionTest(success=False, message="Unsupported platform")
    except Exception as e:
        return StoreConnectionTest(success=False, message=str(e))


async def test_shopify_connection(store: StoreCreate) -> StoreConnectionTest:
    if not SHOPIFY_AVAILABLE:
        return StoreConnectionTest(
            success=False, message="Shopify API not available. Install with: pip install ShopifyAPI"
        )

    try:
        # Use httpx to make a direct API request
        api_version = "2023-07"
        url = f"{store.store_url}/admin/api/{api_version}/shop.json"

        headers = {"X-Shopify-Access-Token": store.api_secret, "Content-Type": "application/json"}

        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers)

            if response.status_code == 200:
                shop_data = response.json()["shop"]
                return StoreConnectionTest(
                    success=True,
                    message="Successfully connected to Shopify store",
                    store_info={"name": shop_data["name"]},
                )
            else:
                return StoreConnectionTest(success=False, message=f"Shopify connection failed: {response.text}")
    except Exception as e:
        return StoreConnectionTest(success=False, message=f"Shopify connection failed: {str(e)}")


async def test_woocommerce_connection(store: StoreCreate) -> StoreConnectionTest:
    if not WOOCOMMERCE_AVAILABLE:
        return StoreConnectionTest(
            success=False, message="WooCommerce API not available. Install with: pip install woocommerce"
        )

    try:
        wcapi = WooCommerceAPI(
            url=store.store_url, consumer_key=store.api_key, consumer_secret=store.api_secret, version="wc/v3"
        )

        # Test connection by fetching system status
        response = wcapi.get("system_status")

        if response.status_code == 200:
            data = response.json()
            return StoreConnectionTest(
                success=True,
                message="Successfully connected to WooCommerce store",
                store_info={
                    "name": data.get("settings", {}).get("title", ""),
                    "version": data.get("version", ""),
                    "url": store.store_url,
                },
            )
        else:
            return StoreConnectionTest(success=False, message=f"WooCommerce connection failed: {response.text}")
    except Exception as e:
        return StoreConnectionTest(success=False, message=f"WooCommerce connection failed: {str(e)}")


@router.post("/{store_id}/sync")
async def sync_store_data(store_id: int, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    store = db.query(Store).filter(Store.id == store_id, Store.owner_id == current_user.id).first()

    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    try:
        if store.platform == "shopify":
            await sync_shopify_data(store, db)
        elif store.platform == "woocommerce":
            await sync_woocommerce_data(store, db)
        else:
            raise HTTPException(status_code=400, detail="Unsupported platform")

        # Update last_sync timestamp
        store.last_sync = datetime.now()
        db.commit()

        return {"message": "Sync completed successfully", "store_id": store_id}
    except Exception as e:
        db.rollback()
        # Log the full error for debugging
        import traceback

        print(f"Sync error: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Sync failed: {str(e)}")


async def sync_shopify_data(store: Store, db: Session):
    """Sync products and orders from a Shopify store"""
    if not SHOPIFY_AVAILABLE:
        raise HTTPException(status_code=400, detail="Shopify API not available. Install with: pip install ShopifyAPI")

    # Use GraphQL for better performance
    await sync_shopify_products_graphql(store, db)
    # await sync_shopify_orders_graphql(store, db)


async def sync_shopify_products_graphql(store: Store, db: Session):
    """Sync products from Shopify using GraphQL"""
    from models import Product

    # GraphQL query to fetch products with pagination
    query = """
    query GetProducts($cursor: String) {
      products(first: 25, after: $cursor) {
        edges {
          node {
            id
            title
            descriptionHtml
            handle
            productType
            tags
            variants(first: 5) {
              edges {
                node {
                  id
                  price
                  compareAtPrice
                  inventoryQuantity
                  sku
                  title
                  availableForSale
                }
              }
            }
            images(first: 1) {
              edges {
                node {
                  url
                  altText
                }
              }
            }
            status
            createdAt
            updatedAt
          }
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
    """

    # Set up API access
    shop_url = store.store_url
    if not shop_url.startswith(("http://", "https://")):
        shop_url = f"https://{shop_url}"
    token = store.api_secret

    headers = {"X-Shopify-Access-Token": token, "Content-Type": "application/json"}

    try:
        # Initialize cursor for pagination
        cursor = None
        has_next_page = True

        while has_next_page:
            variables = {"cursor": cursor} if cursor else {}

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{shop_url}/admin/api/2023-07/graphql.json",
                    json={"query": query, "variables": variables},
                    headers=headers,
                )

                if response.status_code != 200:
                    raise Exception(f"GraphQL request failed: {response.text}")

                data = response.json()

                if "errors" in data:
                    raise Exception(f"GraphQL errors: {data['errors']}")

                products_data = data.get("data", {}).get("products", {})
                page_info = products_data.get("pageInfo", {})

                # Process products
                for edge in products_data.get("edges", []):
                    product = edge.get("node", {})
                    external_id = product.get("id", "").split("/")[-1]

                    if not external_id:
                        continue

                    # Get first variant for price and inventory
                    variants = product.get("variants", {}).get("edges", [])
                    variant = variants[0].get("node", {}) if variants else {}

                    # Get first image if available
                    images = product.get("images", {}).get("edges", [])
                    image_url = images[0].get("node", {}).get("url", "") if images else ""

                    # Check if product exists
                    existing_product = (
                        db.query(Product)
                        .filter(Product.external_id == external_id, Product.store_id == store.id)
                        .first()
                    )

                    title = product.get("title", "")
                    description = product.get("descriptionHtml", "")
                    price = float(variant.get("price", 0)) if variant.get("price") else 0
                    inventory = variant.get("inventoryQuantity", 0) or 0
                    sku = variant.get("sku", "")
                    status = product.get("status", "").lower()

                    if existing_product:
                        # Update existing product
                        existing_product.title = title
                        existing_product.description = description
                        existing_product.price = price
                        existing_product.inventory_quantity = inventory
                        existing_product.sku = sku
                        existing_product.status = status
                    else:
                        # Create new product
                        new_product = Product(
                            external_id=external_id,
                            title=title,
                            description=description,
                            price=price,
                            inventory_quantity=inventory,
                            sku=sku,
                            status=status,
                            store_id=store.id,
                        )
                        db.add(new_product)

                # Update cursor for next page
                has_next_page = page_info.get("hasNextPage", False)
                cursor = page_info.get("endCursor") if has_next_page else None

                # Commit after each page to avoid large transactions
                db.commit()

    except Exception as e:
        db.rollback()
        raise Exception(f"Error syncing Shopify products: {str(e)}")


async def sync_shopify_orders_graphql(store: Store, db: Session):
    """Sync orders from Shopify using GraphQL"""
    from models import Order

    # GraphQL query to fetch orders
    query = """
    {
      orders(first: 50, sortKey: CREATED_AT, reverse: true) {
        edges {
          node {
            id
            name
            createdAt
            totalPriceSet {
              shopMoney {
                amount
              }
            }
            customer {
              firstName
              lastName
              email
            }
            fulfillmentStatus
            financialStatus
          }
        }
      }
    }
    """

    # Set up API access
    shop_url = store.store_url
    if not shop_url.startswith(("http://", "https://")):
        shop_url = f"https://{shop_url}"
    token = store.api_secret

    headers = {"X-Shopify-Access-Token": token, "Content-Type": "application/json"}

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{shop_url}/admin/api/2023-07/graphql.json", json={"query": query}, headers=headers
            )

            if response.status_code != 200:
                raise Exception(f"GraphQL request failed: {response.text}")

            data = response.json()

            if "errors" in data:
                raise Exception(f"GraphQL errors: {data['errors']}")

            # Process orders
            for edge in data.get("data", {}).get("orders", {}).get("edges", []):
                order = edge.get("node", {})
                external_id = order.get("id", "").split("/")[-1]

                if not external_id:
                    continue

                # Check if order exists
                existing_order = (
                    db.query(Order).filter(Order.external_id == external_id, Order.store_id == store.id).first()
                )

                customer = order.get("customer", {})
                customer_name = f"{customer.get('firstName', '')} {customer.get('lastName', '')}".strip()

                order_number = order.get("name", "")
                total_price_set = order.get("totalPriceSet", {}).get("shopMoney", {})
                total_price = float(total_price_set.get("amount", 0)) if total_price_set else 0
                customer_email = customer.get("email", "")
                status = order.get("fulfillmentStatus") or "unfulfilled"
                financial_status = order.get("financialStatus", "")
                order_date = order.get("createdAt", "")

                if existing_order:
                    # Update existing order
                    existing_order.order_number = order_number
                    existing_order.total_price = total_price
                    existing_order.customer_name = customer_name
                    existing_order.customer_email = customer_email
                    existing_order.status = status
                    existing_order.financial_status = financial_status
                else:
                    # Create new order
                    new_order = Order(
                        external_id=external_id,
                        order_number=order_number,
                        total_price=total_price,
                        customer_name=customer_name,
                        customer_email=customer_email,
                        status=status,
                        financial_status=financial_status,
                        store_id=store.id,
                        order_date=order_date,
                    )
                    db.add(new_order)

            db.commit()
    except Exception as e:
        db.rollback()
        raise Exception(f"Error syncing Shopify orders: {str(e)}")


async def sync_woocommerce_data(store: Store, db: Session):
    """Sync products and orders from a WooCommerce store"""
    if not WOOCOMMERCE_AVAILABLE:
        raise HTTPException(
            status_code=400, detail="WooCommerce API not available. Install with: pip install woocommerce"
        )

    await sync_woocommerce_products(store, db)
    await sync_woocommerce_orders(store, db)


async def sync_woocommerce_products(store: Store, db: Session):
    """Sync products from WooCommerce"""
    from models import Product

    try:
        wcapi = WooCommerceAPI(
            url=store.store_url, consumer_key=store.api_key, consumer_secret=store.api_secret, version="wc/v3"
        )

        # WooCommerce API is synchronous, so run in a thread pool
        def get_products():
            try:
                response = wcapi.get("products", params={"per_page": 50})
                if response.status_code != 200:
                    raise Exception(f"WooCommerce API error: {response.text}")
                return response.json()
            except Exception as e:
                raise Exception(f"WooCommerce API request failed: {str(e)}")

        # Run in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        products = await loop.run_in_executor(None, get_products)

        for product in products:
            # Check if product exists
            product_id = str(product.get("id", ""))
            if not product_id:
                continue

            existing_product = (
                db.query(Product).filter(Product.external_id == product_id, Product.store_id == store.id).first()
            )

            name = product.get("name", "")
            description = product.get("description", "")
            price = float(product.get("price", 0)) if product.get("price") else 0
            stock = product.get("stock_quantity") or 0
            sku = product.get("sku", "") or ""
            status = product.get("status", "")

            if existing_product:
                # Update existing product
                existing_product.title = name
                existing_product.description = description
                existing_product.price = price
                existing_product.inventory_quantity = stock
                existing_product.sku = sku
                existing_product.status = status
            else:
                # Create new product
                new_product = Product(
                    external_id=product_id,
                    title=name,
                    description=description,
                    price=price,
                    inventory_quantity=stock,
                    sku=sku,
                    status=status,
                    store_id=store.id,
                )
                db.add(new_product)

        db.commit()
    except Exception as e:
        db.rollback()
        raise Exception(f"Error syncing WooCommerce products: {str(e)}")


async def sync_woocommerce_orders(store: Store, db: Session):
    """Sync orders from WooCommerce"""
    from models import Order

    wcapi = WooCommerceAPI(
        url=store.store_url, consumer_key=store.api_key, consumer_secret=store.api_secret, version="wc/v3"
    )

    # WooCommerce API is synchronous, so run in a thread pool
    def get_orders():
        return wcapi.get("orders", params={"per_page": 50}).json()

    # Run in thread pool to avoid blocking
    loop = asyncio.get_event_loop()
    orders = await loop.run_in_executor(None, get_orders)

    for order in orders:
        # Check if order exists
        existing_order = (
            db.query(Order).filter(Order.external_id == str(order["id"]), Order.store_id == store.id).first()
        )

        if existing_order:
            # Update existing order
            existing_order.order_number = order["number"]
            existing_order.total_price = float(order["total"])
            existing_order.customer_name = f"{order['billing']['first_name']} {order['billing']['last_name']}".strip()
            existing_order.customer_email = order["billing"]["email"]
            existing_order.status = order["status"]
            existing_order.financial_status = "paid" if order["date_paid"] else "pending"
        else:
            # Create new order
            new_order = Order(
                external_id=str(order["id"]),
                order_number=order["number"],
                total_price=float(order["total"]),
                customer_name=f"{order['billing']['first_name']} {order['billing']['last_name']}".strip(),
                customer_email=order["billing"]["email"],
                status=order["status"],
                financial_status="paid" if order["date_paid"] else "pending",
                store_id=store.id,
                order_date=order["date_created"],
            )
            db.add(new_order)

    db.commit()
