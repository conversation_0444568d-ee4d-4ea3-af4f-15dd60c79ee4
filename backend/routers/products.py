from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel

from database import get_db
from models import User, Store, Product
from routers.auth import get_current_user

router = APIRouter()

class ProductResponse(BaseModel):
    id: int
    external_id: str
    title: str
    description: Optional[str] = None
    price: float
    inventory_quantity: int
    sku: Optional[str] = None
    status: str
    store_id: int

@router.get("/", response_model=List[ProductResponse])
async def get_products(
    store_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    query = db.query(Product).join(Store).filter(Store.owner_id == current_user.id)
    
    if store_id:
        query = query.filter(Product.store_id == store_id)
    
    products = query.all()
    return products

@router.get("/{product_id}", response_model=ProductResponse)
async def get_product(
    product_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    product = db.query(Product).join(Store).filter(
        Product.id == product_id,
        Store.owner_id == current_user.id
    ).first()
    
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")
    
    return product
