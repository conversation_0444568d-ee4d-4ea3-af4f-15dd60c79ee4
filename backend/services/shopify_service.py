"""
Shopify GraphQL API Service
Handles all interactions with Shopify Admin and Storefront GraphQL APIs
"""

import httpx
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class ShopifyGraphQLService:
    """Service for interacting with Shopify GraphQL APIs"""
    
    def __init__(self, shop_domain: str, admin_access_token: str, storefront_access_token: Optional[str] = None):
        self.shop_domain = shop_domain
        self.admin_access_token = admin_access_token
        self.storefront_access_token = storefront_access_token
        self.admin_api_version = "2025-04"
        self.storefront_api_version = "2025-04"
        
        # API endpoints
        self.admin_endpoint = f"https://{shop_domain}/admin/api/{self.admin_api_version}/graphql.json"
        self.storefront_endpoint = f"https://{shop_domain}/api/{self.storefront_api_version}/graphql.json"
    
    async def test_admin_connection(self) -> Dict[str, Any]:
        """Test connection to Shopify Admin API"""
        query = """
        query {
            shop {
                id
                name
                email
                domain
                myshopifyDomain
                plan {
                    displayName
                }
                currencyCode
            }
        }
        """
        
        try:
            result = await self._execute_admin_query(query)
            if result.get("data", {}).get("shop"):
                return {
                    "success": True,
                    "message": "Successfully connected to Shopify Admin API",
                    "shop_info": result["data"]["shop"]
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to retrieve shop information",
                    "errors": result.get("errors", [])
                }
        except Exception as e:
            logger.error(f"Admin API connection test failed: {str(e)}")
            return {
                "success": False,
                "message": f"Connection failed: {str(e)}"
            }
    
    async def test_storefront_connection(self) -> Dict[str, Any]:
        """Test connection to Shopify Storefront API"""
        if not self.storefront_access_token:
            return {
                "success": False,
                "message": "Storefront access token not provided"
            }
        
        query = """
        query {
            shop {
                name
                primaryDomain {
                    host
                }
            }
        }
        """
        
        try:
            result = await self._execute_storefront_query(query)
            if result.get("data", {}).get("shop"):
                return {
                    "success": True,
                    "message": "Successfully connected to Shopify Storefront API",
                    "shop_info": result["data"]["shop"]
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to retrieve shop information from Storefront API",
                    "errors": result.get("errors", [])
                }
        except Exception as e:
            logger.error(f"Storefront API connection test failed: {str(e)}")
            return {
                "success": False,
                "message": f"Storefront connection failed: {str(e)}"
            }
    
    async def get_shop_info(self) -> Dict[str, Any]:
        """Get comprehensive shop information"""
        query = """
        query {
            shop {
                id
                name
                email
                domain
                myshopifyDomain
                plan {
                    displayName
                }
                currencyCode
                timezoneAbbreviation
                weightUnit
                billingAddress {
                    country
                    countryCodeV2
                }
            }
        }
        """
        
        result = await self._execute_admin_query(query)
        return result.get("data", {}).get("shop", {})
    
    async def get_products(self, limit: int = 50, cursor: Optional[str] = None) -> Dict[str, Any]:
        """Get products using GraphQL Admin API"""
        after_clause = f', after: "{cursor}"' if cursor else ""
        
        query = f"""
        query {{
            products(first: {limit}{after_clause}) {{
                edges {{
                    node {{
                        id
                        title
                        description
                        descriptionHtml
                        handle
                        productType
                        vendor
                        status
                        tags
                        createdAt
                        updatedAt
                        images(first: 10) {{
                            edges {{
                                node {{
                                    id
                                    url
                                    altText
                                }}
                            }}
                        }}
                        variants(first: 10) {{
                            edges {{
                                node {{
                                    id
                                    title
                                    price
                                    compareAtPrice
                                    sku
                                    barcode
                                    inventoryQuantity
                                    weight
                                    weightUnit
                                }}
                            }}
                        }}
                    }}
                    cursor
                }}
                pageInfo {{
                    hasNextPage
                    endCursor
                }}
            }}
        }}
        """
        
        result = await self._execute_admin_query(query)
        return result.get("data", {}).get("products", {})
    
    async def get_orders(self, limit: int = 50, cursor: Optional[str] = None) -> Dict[str, Any]:
        """Get orders using GraphQL Admin API"""
        after_clause = f', after: "{cursor}"' if cursor else ""
        
        query = f"""
        query {{
            orders(first: {limit}{after_clause}) {{
                edges {{
                    node {{
                        id
                        name
                        email
                        createdAt
                        updatedAt
                        totalPriceSet {{
                            shopMoney {{
                                amount
                                currencyCode
                            }}
                        }}
                        subtotalPriceSet {{
                            shopMoney {{
                                amount
                                currencyCode
                            }}
                        }}
                        totalTaxSet {{
                            shopMoney {{
                                amount
                                currencyCode
                            }}
                        }}
                        totalDiscountsSet {{
                            shopMoney {{
                                amount
                                currencyCode
                            }}
                        }}
                        displayFinancialStatus
                        displayFulfillmentStatus
                        customer {{
                            id
                            email
                        }}
                        lineItems(first: 50) {{
                            edges {{
                                node {{
                                    id
                                    title
                                    quantity
                                    variant {{
                                        id
                                        product {{
                                            id
                                        }}
                                    }}
                                    originalTotalSet {{
                                        shopMoney {{
                                            amount
                                            currencyCode
                                        }}
                                    }}
                                }}
                            }}
                        }}
                    }}
                    cursor
                }}
                pageInfo {{
                    hasNextPage
                    endCursor
                }}
            }}
        }}
        """
        
        result = await self._execute_admin_query(query)
        return result.get("data", {}).get("orders", {})
    
    async def _execute_admin_query(self, query: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        """Execute a GraphQL query against the Admin API"""
        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": self.admin_access_token
        }
        
        payload = {"query": query}
        if variables:
            payload["variables"] = variables
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                self.admin_endpoint,
                headers=headers,
                json=payload,
                timeout=30.0
            )
            response.raise_for_status()
            return response.json()
    
    async def _execute_storefront_query(self, query: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        """Execute a GraphQL query against the Storefront API"""
        if not self.storefront_access_token:
            raise ValueError("Storefront access token is required")
        
        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Storefront-Access-Token": self.storefront_access_token
        }
        
        payload = {"query": query}
        if variables:
            payload["variables"] = variables
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                self.storefront_endpoint,
                headers=headers,
                json=payload,
                timeout=30.0
            )
            response.raise_for_status()
            return response.json()
