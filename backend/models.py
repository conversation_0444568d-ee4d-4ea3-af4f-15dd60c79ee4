from sqlalchemy import Column, Integer, String, DateTime, Float, <PERSON><PERSON><PERSON>, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    stores = relationship("Store", back_populates="owner")


class Store(Base):
    __tablename__ = "stores"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)  # Store name (e.g., "my-store" for my-store.myshopify.com)
    platform = Column(String, nullable=False, default="shopify")  # Focus on Shopify only
    admin_access_token = Column(String, nullable=False)  # Admin API access token
    storefront_access_token = Column(String)  # Storefront API access token (optional)
    shop_domain = Column(String)  # Full domain (e.g., "my-store.myshopify.com")
    shop_id = Column(String)  # Shopify shop ID
    shop_name = Column(String)  # Display name from Shopify
    is_active = Column(Boolean, default=True)
    last_sync = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    owner_id = Column(Integer, ForeignKey("users.id"))
    owner = relationship("User", back_populates="stores")
    products = relationship("Product", back_populates="store")
    orders = relationship("Order", back_populates="store")


class Product(Base):
    __tablename__ = "products"

    id = Column(Integer, primary_key=True, index=True)
    external_id = Column(String, nullable=False)  # Shopify Product ID
    title = Column(String, nullable=False)
    description = Column(Text)
    description_html = Column(Text)
    handle = Column(String)  # URL handle
    price = Column(Float)
    compare_at_price = Column(Float)  # Original price for discounts
    inventory_quantity = Column(Integer, default=0)
    sku = Column(String)
    barcode = Column(String)
    weight = Column(Float)
    weight_unit = Column(String, default="kg")
    status = Column(String, default="active")
    product_type = Column(String)
    vendor = Column(String)
    tags = Column(Text)  # JSON string of tags
    images = Column(Text)  # JSON string of image URLs
    variants = Column(Text)  # JSON string of variants
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    store_id = Column(Integer, ForeignKey("stores.id"))
    store = relationship("Store", back_populates="products")


class Order(Base):
    __tablename__ = "orders"

    id = Column(Integer, primary_key=True, index=True)
    external_id = Column(String, nullable=False)  # Shopify Order ID
    order_number = Column(String)
    customer_email = Column(String)
    customer_id = Column(String)  # Shopify Customer ID
    total_price = Column(Float)
    subtotal_price = Column(Float)
    total_tax = Column(Float)
    total_discounts = Column(Float)
    currency = Column(String, default="USD")
    status = Column(String)
    financial_status = Column(String)
    fulfillment_status = Column(String)
    order_date = Column(DateTime(timezone=True))
    line_items = Column(Text)  # JSON string of line items
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    store_id = Column(Integer, ForeignKey("stores.id"))
    store = relationship("Store", back_populates="orders")


class SalesData(Base):
    __tablename__ = "sales_data"

    id = Column(Integer, primary_key=True, index=True)
    date = Column(DateTime(timezone=True), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"))
    quantity_sold = Column(Integer, default=0)
    revenue = Column(Float, default=0.0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    product = relationship("Product")


class ForecastData(Base):
    __tablename__ = "forecast_data"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"))
    forecast_date = Column(DateTime(timezone=True), nullable=False)
    predicted_sales = Column(Float)
    lower_bound = Column(Float)
    upper_bound = Column(Float)
    confidence_interval = Column(Float, default=0.95)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    product = relationship("Product")
