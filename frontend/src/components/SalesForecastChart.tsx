import React from "react";
import { TrendingUp, Calendar, Target, AlertCircle } from "lucide-react";

// Temporarily commented out Chart.js imports to debug
// import {
//   Chart as ChartJS,
//   CategoryScale,
//   LinearScale,
//   PointElement,
//   LineElement,
//   Title,
//   Tooltip,
//   Legend,
//   Filler,
//   ChartOptions,
//   ChartData,
// } from 'chart.js';
// import { Line } from 'react-chartjs-2';

// ChartJS.register(
//   CategoryScale,
//   LinearScale,
//   PointElement,
//   LineElement,
//   Title,
//   Tooltip,
//   Legend,
//   Filler
// );

interface ForecastData {
  date: string;
  predicted_sales: number;
  lower_bound: number;
  upper_bound: number;
}

interface ProductInfo {
  id: number;
  title: string;
  sku?: string;
  current_inventory: number;
}

interface SalesForecastChartProps {
  forecastData: ForecastData[];
  productInfo: ProductInfo;
  isLoading?: boolean;
  error?: string;
  onRefresh?: () => void;
}

const SalesForecastChart: React.FC<SalesForecastChartProps> = ({
  forecastData,
  productInfo,
  isLoading = false,
  error,
  onRefresh,
}) => {
  const chartRef = useRef<ChartJS<"line">>(null);

  // Prepare chart data
  const chartData: ChartData<"line"> = {
    labels: forecastData.map((item) => {
      const date = new Date(item.date);
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
    }),
    datasets: [
      {
        label: "Predicted Sales",
        data: forecastData.map((item) => item.predicted_sales),
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.1)",
        borderWidth: 3,
        fill: false,
        tension: 0.4,
        pointRadius: 4,
        pointHoverRadius: 8,
        pointBackgroundColor: "rgb(59, 130, 246)",
        pointBorderColor: "#ffffff",
        pointBorderWidth: 2,
      },
      {
        label: "Confidence Interval",
        data: forecastData.map((item) => item.upper_bound),
        borderColor: "rgba(34, 197, 94, 0.3)",
        backgroundColor: "rgba(34, 197, 94, 0.1)",
        borderWidth: 1,
        fill: "+1",
        tension: 0.4,
        pointRadius: 0,
        pointHoverRadius: 0,
      },
      {
        label: "Lower Bound",
        data: forecastData.map((item) => item.lower_bound),
        borderColor: "rgba(34, 197, 94, 0.3)",
        backgroundColor: "rgba(34, 197, 94, 0.1)",
        borderWidth: 1,
        fill: false,
        tension: 0.4,
        pointRadius: 0,
        pointHoverRadius: 0,
      },
    ],
  };

  // Chart options for interactivity
  const options: ChartOptions<"line"> = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: "index",
      intersect: false,
    },
    plugins: {
      title: {
        display: true,
        text: `Sales Forecast - ${productInfo.title}`,
        font: {
          size: 18,
          weight: "bold",
        },
        color: "#1f2937",
        padding: 20,
      },
      legend: {
        display: true,
        position: "top",
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
          },
          filter: (legendItem) => {
            // Hide the lower bound from legend
            return legendItem.text !== "Lower Bound";
          },
        },
      },
      tooltip: {
        mode: "index",
        intersect: false,
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "#ffffff",
        bodyColor: "#ffffff",
        borderColor: "rgba(59, 130, 246, 0.5)",
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
        callbacks: {
          title: (context) => {
            const index = context[0].dataIndex;
            const date = new Date(forecastData[index].date);
            return date.toLocaleDateString("en-US", {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
            });
          },
          label: (context) => {
            const label = context.dataset.label || "";
            const value = context.parsed.y;

            if (label === "Predicted Sales") {
              return `${label}: ${value.toFixed(1)} units`;
            } else if (label === "Confidence Interval") {
              const index = context.dataIndex;
              const lowerBound = forecastData[index].lower_bound;
              const upperBound = forecastData[index].upper_bound;
              return `Range: ${lowerBound.toFixed(1)} - ${upperBound.toFixed(
                1
              )} units`;
            }
            return "";
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: "Date",
          font: {
            size: 14,
            weight: "bold",
          },
          color: "#374151",
        },
        grid: {
          color: "rgba(156, 163, 175, 0.2)",
        },
        ticks: {
          color: "#6b7280",
          font: {
            size: 11,
          },
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: "Predicted Sales (Units)",
          font: {
            size: 14,
            weight: "bold",
          },
          color: "#374151",
        },
        beginAtZero: true,
        grid: {
          color: "rgba(156, 163, 175, 0.2)",
        },
        ticks: {
          color: "#6b7280",
          font: {
            size: 11,
          },
          callback: function (value) {
            return Math.round(Number(value));
          },
        },
      },
    },
    elements: {
      point: {
        radius: 4,
        hoverRadius: 8,
      },
      line: {
        borderJoinStyle: "round",
      },
    },
    animation: {
      duration: 1000,
      easing: "easeInOutQuart",
    },
  };

  // Calculate summary statistics
  const totalPredictedSales = forecastData.reduce(
    (sum, item) => sum + item.predicted_sales,
    0
  );
  const avgDailySales =
    forecastData.length > 0 ? totalPredictedSales / forecastData.length : 0;
  const maxSalesDay =
    forecastData.length > 0
      ? forecastData.reduce((max, item) =>
          item.predicted_sales > max.predicted_sales ? item : max
        )
      : { predicted_sales: 0, date: new Date().toISOString() };

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-center text-red-500 mb-4">
          <AlertCircle size={48} />
        </div>
        <h3 className="text-lg font-semibold text-center mb-2">
          Forecast Error
        </h3>
        <p className="text-gray-600 text-center mb-4">{error}</p>
        {onRefresh && (
          <button
            onClick={onRefresh}
            className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors"
          >
            Try Again
          </button>
        )}
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="h-64 bg-gray-200 rounded mb-4"></div>
          <div className="grid grid-cols-3 gap-4">
            <div className="h-16 bg-gray-200 rounded"></div>
            <div className="h-16 bg-gray-200 rounded"></div>
            <div className="h-16 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      {/* Chart Placeholder */}
      <div className="h-80 mb-6 bg-gray-100 rounded-lg flex items-center justify-center">
        <div className="text-center">
          <TrendingUp className="mx-auto text-gray-400 mb-2" size={48} />
          <p className="text-gray-600">Interactive Chart Coming Soon</p>
          <p className="text-sm text-gray-500">
            Chart.js integration temporarily disabled for debugging
          </p>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-blue-600 font-medium">
                Total Forecast
              </p>
              <p className="text-2xl font-bold text-blue-900">
                {totalPredictedSales.toFixed(0)} units
              </p>
            </div>
            <TrendingUp className="text-blue-500" size={24} />
          </div>
        </div>

        <div className="bg-green-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-green-600 font-medium">
                Daily Average
              </p>
              <p className="text-2xl font-bold text-green-900">
                {avgDailySales.toFixed(1)} units
              </p>
            </div>
            <Target className="text-green-500" size={24} />
          </div>
        </div>

        <div className="bg-purple-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-purple-600 font-medium">Peak Day</p>
              <p className="text-lg font-bold text-purple-900">
                {maxSalesDay.predicted_sales.toFixed(0)} units
              </p>
              <p className="text-xs text-purple-600">
                {new Date(maxSalesDay.date).toLocaleDateString()}
              </p>
            </div>
            <Calendar className="text-purple-500" size={24} />
          </div>
        </div>
      </div>

      {/* Product Info */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-semibold text-gray-900 mb-2">
          Product Information
        </h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">SKU:</span>
            <span className="ml-2 font-medium">{productInfo.sku || "N/A"}</span>
          </div>
          <div>
            <span className="text-gray-600">Current Inventory:</span>
            <span className="ml-2 font-medium">
              {productInfo.current_inventory} units
            </span>
          </div>
        </div>
      </div>

      {/* Refresh Button */}
      {onRefresh && (
        <div className="mt-4 text-center">
          <button
            onClick={onRefresh}
            className="bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors text-sm"
          >
            Refresh Forecast
          </button>
        </div>
      )}
    </div>
  );
};

export default SalesForecastChart;
