import React, { useState, useEffect } from "react";
import {
  Store,
  Plus,
  RefreshCw,
  CheckCircle,
  XCircle,
  Trash2,
} from "lucide-react";
import { storeService } from "../services/storeService";
import type { Store as StoreType } from "../services/storeService";

const Stores: React.FC = () => {
  const [stores, setStores] = useState<StoreType[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
    admin_access_token: "",
    storefront_access_token: "",
  });
  const [testResult, setTestResult] = useState<any>(null);
  const [testing, setTesting] = useState(false);

  useEffect(() => {
    loadStores();
  }, []);

  const loadStores = async () => {
    try {
      const data = await storeService.getStores();
      setStores(data);
    } catch (error) {
      console.error("Failed to load stores:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    setTesting(true);
    setTestResult(null);

    try {
      const result = await storeService.testConnection(formData);
      setTestResult(result);
    } catch (error) {
      setTestResult({ success: false, message: "Connection test failed" });
    } finally {
      setTesting(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      await storeService.createStore(formData);
      setShowAddForm(false);
      setFormData({
        name: "",
        admin_access_token: "",
        storefront_access_token: "",
      });
      setTestResult(null);
      loadStores();
    } catch (error) {
      console.error("Failed to create store:", error);
    }
  };

  const handleSync = async (storeId: number) => {
    try {
      await storeService.syncStore(storeId);
      // Refresh stores to update last_sync
      loadStores();
    } catch (error) {
      console.error("Failed to sync store:", error);
    }
  };

  const handleDelete = async (storeId: number, storeName: string) => {
    if (
      window.confirm(
        `Are you sure you want to delete "${storeName}"? This will also delete all associated products, orders, and forecast data.`
      )
    ) {
      try {
        await storeService.deleteStore(storeId);
        loadStores();
      } catch (error) {
        console.error("Failed to delete store:", error);
      }
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Your Stores</h1>
          <p className="text-gray-600">
            Connect and manage your Shopify stores with advanced analytics
          </p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center space-x-2"
        >
          <Plus size={20} />
          <span>Add Store</span>
        </button>
      </div>

      {/* Add Store Form */}
      {showAddForm && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Connect Shopify Store</h2>
          <p className="text-gray-600 mb-6">
            Connect your Shopify store using the new GraphQL Admin API. You'll
            need your store name and access tokens.
          </p>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Store Name
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  placeholder="my-store"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <span className="text-gray-500 text-sm">.myshopify.com</span>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Enter just the store name (e.g., "my-store" for
                my-store.myshopify.com)
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Admin API Access Token
              </label>
              <input
                type="password"
                value={formData.admin_access_token}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    admin_access_token: e.target.value,
                  })
                }
                placeholder="shpat_..."
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                Required for accessing products, orders, and store data
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Storefront API Access Token (Optional)
              </label>
              <input
                type="password"
                value={formData.storefront_access_token}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    storefront_access_token: e.target.value,
                  })
                }
                placeholder="Optional for public storefront data"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                Optional: For accessing public storefront data
              </p>
            </div>

            {/* Test Connection */}
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={handleTestConnection}
                disabled={testing}
                className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 disabled:opacity-50"
              >
                {testing ? "Testing..." : "Test Connection"}
              </button>

              {testResult && (
                <div
                  className={`flex items-center space-x-2 ${
                    testResult.success ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {testResult.success ? (
                    <CheckCircle size={20} />
                  ) : (
                    <XCircle size={20} />
                  )}
                  <span>{testResult.message}</span>
                </div>
              )}
            </div>

            <div className="flex space-x-4">
              <button
                type="submit"
                disabled={!testResult?.success}
                className="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
              >
                Add Store
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowAddForm(false);
                  setTestResult(null);
                }}
                className="bg-gray-300 text-gray-700 px-6 py-2 rounded hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Stores List */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {stores.map((store) => (
          <div key={store.id} className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <Store className="text-blue-500" size={24} />
                <div>
                  <h3 className="font-semibold">
                    {store.shop_name || store.name}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {store.shop_domain || `${store.name}.myshopify.com`}
                  </p>
                </div>
              </div>
              <div
                className={`w-3 h-3 rounded-full ${
                  store.is_active ? "bg-green-500" : "bg-red-500"
                }`}
              ></div>
            </div>

            <div className="mb-4">
              <span className="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                Shopify
              </span>
            </div>

            <div className="flex justify-between items-center mb-4">
              <span className="text-xs text-gray-500">
                {store.last_sync
                  ? `Last sync: ${new Date(
                      store.last_sync
                    ).toLocaleDateString()}`
                  : "Never synced"}
              </span>
            </div>

            <div className="flex space-x-2">
              <button
                onClick={() => handleSync(store.id)}
                className="flex-1 flex items-center justify-center space-x-1 text-blue-500 hover:text-blue-600 bg-blue-50 hover:bg-blue-100 py-2 px-3 rounded-lg transition-colors"
              >
                <RefreshCw size={16} />
                <span>Sync</span>
              </button>
              <button
                onClick={() =>
                  handleDelete(store.id, store.shop_name || store.name)
                }
                className="flex items-center justify-center text-red-500 hover:text-red-600 bg-red-50 hover:bg-red-100 py-2 px-3 rounded-lg transition-colors"
              >
                <Trash2 size={16} />
              </button>
            </div>
          </div>
        ))}
      </div>

      {stores.length === 0 && (
        <div className="text-center py-12">
          <Store className="mx-auto text-gray-400 mb-4" size={64} />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">
            No stores connected
          </h3>
          <p className="text-gray-500 mb-4">
            Connect your first Shopify store to get started with sales
            forecasting
          </p>
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600"
          >
            Add Your First Store
          </button>
        </div>
      )}
    </div>
  );
};

export default Stores;
